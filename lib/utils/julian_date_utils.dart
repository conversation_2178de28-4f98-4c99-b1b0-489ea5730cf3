import 'package:sweph/sweph.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

import 'LoggerUtils.dart';
import 'TimezoneService.dart';

/// 儒略日計算工具類
/// 提供日期時間與儒略日之間的轉換功能
class JulianDateUtils {
  /// 將日期時間轉換為儒略日
  ///
  /// [dateTime] - 要轉換的日期時間
  /// [latitude] - 緯度（用於計算時區）
  /// [longitude] - 經度（用於計算時區）
  /// 返回儒略日數值
  static Future<double> dateTimeToJulianDay(
    DateTime dateTime,
    double latitude,
    double longitude,
  ) async {
    print(
        '開始轉換日期時間為儒略日: ${dateTime.toString()}, 緯度: $latitude, 經度: $longitude');

    try {
      // 計算時區偏移
      print('計算時區偏移');
      double offset = await calculateZonedDateTimeOffset(
        latitude,
        longitude,
        dateTime.year,
        dateTime.month,
        dateTime.day,
        dateTime.hour,
        dateTime.minute,
      );
      double hours =
          (dateTime.hour + dateTime.minute / 60.0 + dateTime.second / 3600.0) -
              offset;
      print('時區偏移: $offset 小時，調整後時間: $hours 小時');

      // 轉換為儒略日
      print('轉換為儒略日');
      final julianDay = Sweph.swe_julday(
        dateTime.year,
        dateTime.month,
        dateTime.day,
        hours,
        CalendarType.SE_GREG_CAL,
      );
      print('儒略日: $julianDay');

      return julianDay;
    } catch (e) {
      logger.e('轉換日期時間為儒略日時出錯: $e');
      throw Exception('轉換日期時間為儒略日時出錯: $e');
    }
  }

  /// 將儒略日轉換為日期時間
  ///
  /// [julianDay] - 要轉換的儒略日
  /// [latitude] - 緯度（用於計算時區）
  /// [longitude] - 經度（用於計算時區）
  /// 返回 DateTime 對象
  static Future<DateTime> julianDayToDateTime(
    double julianDay,
    double latitude,
    double longitude,
  ) async {
    print('開始轉換儒略日為日期時間: $julianDay, 緯度: $latitude, 經度: $longitude');

    try {
      // 轉換為日期時間
      print('轉換為日期時間');
      final dateTime =
          Sweph.swe_jdut1_to_utc(julianDay, CalendarType.SE_GREG_CAL);
      print('UTC 日期時間: $dateTime');

      // 計算時區偏移
      print('計算時區偏移');
      double offset = await calculateZonedDateTimeOffset(
        latitude,
        longitude,
        dateTime.year,
        dateTime.month,
        dateTime.day,
        dateTime.hour,
        dateTime.minute,
      );

      // 調整為本地時間
      final localDateTime = DateTime(
        dateTime.year,
        dateTime.month,
        dateTime.day,
        dateTime.hour + offset.round(),
        dateTime.minute,
        dateTime.second,
      );
      print('本地日期時間: $localDateTime');

      return localDateTime;
    } catch (e) {
      logger.e('轉換儒略日為日期時間時出錯: $e');
      throw Exception('轉換儒略日為日期時間時出錯: $e');
    }
  }

  /// 計算時區偏移量
  static Future<double> calculateZonedDateTimeOffset(
    double latitude,
    double longitude,
    int year,
    int month,
    int day,
    int hour,
    int minute,
  ) async {
    // 初始化時區數據
    tz.initializeTimeZones();

    // 使用 TimezoneService 來根據經緯度獲取時區名稱
    String? timezone =
        await TimezoneService().getTimeZoneFromLatLng(latitude, longitude);
    if (timezone != null) {
      print("時區: $timezone");
    } else {
      logger.e("找不到對應時區");
      return 0;
    }

    final tz.Location location = tz.getLocation(timezone);

    // 創建對應的 ZonedDateTime
    final tz.TZDateTime zonedDateTime =
        tz.TZDateTime(location, year, month, day, hour, minute);

    // 獲取時區偏移量（包含夏令時影響）
    final int offsetInSeconds = zonedDateTime.timeZoneOffset.inSeconds;

    // 將秒數轉換為小時
    final double offsetInHours = offsetInSeconds / 3600.0;

    return offsetInHours;
  }
}
