import 'package:flutter/material.dart' hide DatePickerTheme;
import 'package:flutter/services.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart';
import 'package:uuid/uuid.dart';

import '../../models/birth_data.dart';
import '../../utils/LoggerUtils.dart';
import '../../utils/geocoding_service.dart';
import '../AppTheme.dart';

/// 出生資料表單頁面，用於創建或編輯出生資料
class BirthDataFormPage extends StatefulWidget {
  final BirthData? initialData;

  const BirthDataFormPage({Key? key, this.initialData}) : super(key: key);

  @override
  State<BirthDataFormPage> createState() => _BirthDataFormPageState();
}

class _BirthDataFormPageState extends State<BirthDataFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _placeController = TextEditingController();
  final _notesController = TextEditingController();
  final _dateController = TextEditingController();
  final _timeController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // 初始化日期和時間控制器
    _updateTextControllers();

    // 如果有初始數據，則填充表單
    if (widget.initialData != null) {
      _nameController.text = widget.initialData!.name;
      _placeController.text = widget.initialData!.birthPlace;
      _notesController.text = widget.initialData!.notes ?? '';
      _selectedDate = widget.initialData!.birthDate;
      _selectedTime = TimeOfDay(
        hour: widget.initialData!.birthDate.hour,
        minute: widget.initialData!.birthDate.minute,
      );

      // 更新日期和時間控制器
      _updateTextControllers();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _placeController.dispose();
    _notesController.dispose();
    _dateController.dispose();
    _timeController.dispose();
    super.dispose();
  }

  // 更新日期和時間文本控制器
  void _updateTextControllers() {
    _dateController.text = '${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')}';
    _timeController.text = '${_selectedTime.hour.toString().padLeft(2, '0')}:${_selectedTime.minute.toString().padLeft(2, '0')}';
  }

  // 根據文本更新日期
  void _updateDateFromText(String text) {
    try {
      // 嘗試解析日期字符串
      final parts = text.split('-');
      if (parts.length == 3) {
        final year = int.tryParse(parts[0]);
        final month = int.tryParse(parts[1]);
        final day = int.tryParse(parts[2]);

        if (year != null && month != null && day != null) {
          // 驗證日期是否有效
          if (_isValidDate(year, month, day)) {
            // 創建新的 DateTime 對象
            final newDate = DateTime(year, month, day,
                _selectedDate.hour, _selectedDate.minute);

            setState(() {
              _selectedDate = newDate;
            });
          }
        }
      }
    } catch (e) {
      // 如果解析失敗，不做任何變更
      logger.e('解析日期失敗: $e');
    }
  }

  // 驗證日期是否有效
  bool _isValidDate(int year, int month, int day) {
    // 基本範圍檢查
    if (year < 1800 || year > DateTime.now().year + 1) return false;
    if (month < 1 || month > 12) return false;
    if (day < 1 || day > 31) return false;

    // 檢查月份天數
    final daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    // 閏年檢查
    bool isLeapYear = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    if (month == 2 && isLeapYear) {
      return day <= 29;
    }

    return day <= daysInMonth[month - 1];
  }

  // 根據文本更新時間
  void _updateTimeFromText(String text) {
    try {
      // 嘗試解析時間字符串
      final parts = text.split(':');
      if (parts.length == 2) {
        final hour = int.tryParse(parts[0]);
        final minute = int.tryParse(parts[1]);

        if (hour != null && minute != null) {
          // 驗證時間是否有效
          if (_isValidTime(hour, minute)) {
            setState(() {
              _selectedTime = TimeOfDay(hour: hour, minute: minute);
            });
          }
        }
      }
    } catch (e) {
      // 如果解析失敗，不做任何變更
      logger.e('解析時間失敗: $e');
    }
  }

  // 驗證時間是否有效
  bool _isValidTime(int hour, int minute) {
    return hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59;
  }

  // 智能日期驗證器
  String? _validateDate(String? value) {
    if (value == null || value.isEmpty) {
      return '請輸入出生日期';
    }

    // 檢查基本格式
    final datePattern = RegExp(r'^\d{4}-\d{2}-\d{2}$');
    if (!datePattern.hasMatch(value)) {
      return '請使用 YYYY-MM-DD 格式（例：1990-01-15）';
    }

    // 解析日期
    final parts = value.split('-');
    final year = int.tryParse(parts[0]);
    final month = int.tryParse(parts[1]);
    final day = int.tryParse(parts[2]);

    if (year == null || month == null || day == null) {
      return '日期格式不正確';
    }

    // 驗證日期有效性
    if (!_isValidDate(year, month, day)) {
      if (year < 1800 || year > DateTime.now().year + 1) {
        return '年份必須在 1800 到 ${DateTime.now().year + 1} 之間';
      }
      if (month < 1 || month > 12) {
        return '月份必須在 01 到 12 之間';
      }
      if (day < 1 || day > 31) {
        return '日期必須在 01 到 31 之間';
      }
      return '這個日期不存在';
    }

    // 檢查是否為未來日期
    final inputDate = DateTime(year, month, day);
    if (inputDate.isAfter(DateTime.now())) {
      return '出生日期不能是未來的日期';
    }

    return null;
  }

  // 智能時間驗證器
  String? _validateTime(String? value) {
    if (value == null || value.isEmpty) {
      return '請輸入出生時間';
    }

    // 檢查基本格式
    final timePattern = RegExp(r'^([01]?[0-9]|2[0-3]):([0-5][0-9])$');
    if (!timePattern.hasMatch(value)) {
      return '請使用 HH:MM 格式（例：14:30）';
    }

    // 解析時間
    final parts = value.split(':');
    final hour = int.tryParse(parts[0]);
    final minute = int.tryParse(parts[1]);

    if (hour == null || minute == null) {
      return '時間格式不正確';
    }

    // 驗證時間有效性
    if (!_isValidTime(hour, minute)) {
      if (hour < 0 || hour > 23) {
        return '小時必須在 00 到 23 之間';
      }
      if (minute < 0 || minute > 59) {
        return '分鐘必須在 00 到 59 之間';
      }
    }

    return null;
  }

  // 選擇日期
  Future<void> _selectDate() async {
    DatePicker.showDatePicker(
      context,
      showTitleActions: true,
      minTime: DateTime(1800, 1, 1),
      maxTime: DateTime.now(),
      onConfirm: (date) {
        setState(() {
          _selectedDate = date;
          // 更新日期文本控制器
          _dateController.text = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
        });
      },
      currentTime: _selectedDate,
      locale: LocaleType.tw,
      theme: const DatePickerTheme(
        backgroundColor: Colors.white,
        itemStyle: TextStyle(color: Colors.black, fontSize: 18),
        doneStyle: TextStyle(color: AppColors.royalIndigo, fontSize: 16),
        cancelStyle: TextStyle(color: Colors.grey, fontSize: 16),
        containerHeight: 210.0,
        titleHeight: 44.0,
        itemHeight: 40.0,
      ),
    );
  }

  // 選擇時間
  Future<void> _selectTime() async {
    DatePicker.showTimePicker(
      context,
      showSecondsColumn: false,
      showTitleActions: true,
      onConfirm: (time) {
        setState(() {
          _selectedTime = TimeOfDay(hour: time.hour, minute: time.minute);
          // 更新時間文本控制器
          _timeController.text = '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
        });
      },
      currentTime: DateTime(
        2000, 1, 1,
        _selectedTime.hour,
        _selectedTime.minute,
      ),
      locale: LocaleType.tw,
      theme: const DatePickerTheme(
        backgroundColor: Colors.white,
        itemStyle: TextStyle(color: Colors.black, fontSize: 18),
        doneStyle: TextStyle(color: AppColors.royalIndigo, fontSize: 16),
        cancelStyle: TextStyle(color: Colors.grey, fontSize: 16),
        containerHeight: 210.0,
        titleHeight: 44.0,
        itemHeight: 40.0,
      ),
    );
  }

  // 獲取當前位置
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 獲取當前位置的經緯度
      final coordinates = await GeocodingService.getCurrentLocation();

      if (coordinates == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('無法獲取當前位置，請確保已開啟位置服務和權限')),
          );
        }
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // 根據經緯度獲取地址
      final address = await GeocodingService.getAddressFromCoordinates(
        coordinates['latitude']!,
        coordinates['longitude']!,
      );

      if (address != null && mounted) {
        setState(() {
          _placeController.text = address;
          _isLoading = false;
        });

        // 顯示成功訊息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已獲取當前位置: $address'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // 如果無法獲取地址，但有經緯度，則直接使用經緯度字符串
        if (mounted) {
          final locationStr = '緯度: ${coordinates['latitude']!.toStringAsFixed(6)}, 經度: ${coordinates['longitude']!.toStringAsFixed(6)}';
          setState(() {
            _placeController.text = locationStr;
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('無法獲取地址名稱，已使用經緯度: $locationStr'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      logger.e('獲取當前位置時出錯: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('獲取當前位置時出錯: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 保存出生資料
  Future<void> _saveBirthData() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 先確保日期和時間已經更新
    _updateDateFromText(_dateController.text);
    _updateTimeFromText(_timeController.text);

    setState(() {
      _isLoading = true;
    });

    try {
      // 使用 GeocodingService 將出生地轉換為經緯度
      Map<String, double>? coordinates = await GeocodingService.getCoordinatesFromAddress(
        _placeController.text,
      );

      if (coordinates == null || !coordinates.containsKey('latitude') || !coordinates.containsKey('longitude')) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('無法獲取出生地的經緯度，請輸入有效的地點')),
          );
        }
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // 創建出生日期時間
      final birthDateTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _selectedTime.hour,
        _selectedTime.minute,
      );

      // 創建或更新出生資料
      final birthData = BirthData(
        id: widget.initialData?.id ?? const Uuid().v4(),
        name: _nameController.text,
        birthDate: birthDateTime,
        birthPlace: _placeController.text,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        latitude: coordinates['latitude']!,
        longitude: coordinates['longitude']!,
      );

      // 返回創建或更新的出生資料
      if (mounted) {
        Navigator.of(context).pop(birthData);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存出生資料時出錯: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isNarrowScreen = screenWidth < 600;

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.person_add,
                color: AppColors.royalIndigo,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              widget.initialData == null ? '新增出生資料' : '編輯出生資料',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                letterSpacing: 0.5,
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? _buildLoadingWidget()
          : Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppColors.scaffoldBackground,
                    AppColors.scaffoldBackground.withValues(alpha: 0.8),
                  ],
                ),
              ),
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: EdgeInsets.all(isNarrowScreen ? 16.0 : 24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 頁面標題和說明
                      _buildPageHeader(),
                      const SizedBox(height: 24),

                      // 姓名輸入
                      _buildNameInput(),
                      const SizedBox(height: 20),

                      // 出生日期和時間
                      _buildDateTimeInput(isNarrowScreen),
                      const SizedBox(height: 20),

                      // 出生地
                      _buildLocationInput(),
                      const SizedBox(height: 20),

                      // 備註
                      _buildNotesInput(),
                      const SizedBox(height: 32),

                      // 保存按鈕
                      _buildSaveButton(),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  /// 構建頁面標題和說明
  Widget _buildPageHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.royalIndigo.withValues(alpha: 0.05),
            AppColors.solarAmber.withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.star_border,
                  color: AppColors.royalIndigo,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '建立星盤檔案',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.royalIndigo,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '請填入準確的出生資訊以獲得精確的占星分析',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建姓名輸入
  Widget _buildNameInput() {
    return _buildInputSection(
      title: '姓名',
      icon: Icons.person_outline,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.royalIndigo.withValues(alpha: 0.3),
          ),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: AppColors.royalIndigo.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TextFormField(
          controller: _nameController,
          decoration: InputDecoration(
            hintText: '請輸入姓名（例：王小明）',
            hintStyle: TextStyle(
              color: Colors.grey[400],
              fontSize: 14,
            ),
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            prefixIcon: Icon(
              Icons.person_outline,
              color: AppColors.royalIndigo.withValues(alpha: 0.7),
              size: 20,
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '請輸入姓名';
            }
            if (value.length < 2) {
              return '姓名至少需要2個字符';
            }
            return null;
          },
        ),
      ),
    );
  }

  /// 構建日期時間輸入
  Widget _buildDateTimeInput(bool isNarrowScreen) {
    return _buildInputSection(
      title: '出生日期和時間',
      icon: Icons.schedule,
      child: Column(
        children: [
          if (isNarrowScreen) ...[
            // 窄屏幕：垂直排列
            _buildDateInput(),
            const SizedBox(height: 16),
            _buildTimeInput(),
          ] else ...[
            // 寬屏幕：水平排列
            IntrinsicHeight(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Expanded(child: _buildDateInput()),
                  const SizedBox(width: 16),
                  Expanded(child: _buildTimeInput()),
                ],
              ),
            ),
          ],
          const SizedBox(height: 16),
          _buildDateTimeHint(),
        ],
      ),
    );
  }

  /// 構建日期輸入
  Widget _buildDateInput() {
    return SmartDateTimeInput(
      label: '出生日期',
      hintText: 'YYYY-MM-DD（例：1990-01-15）',
      controller: _dateController,
      validator: _validateDate,
      onChanged: _updateDateFromText,
      onTap: _selectDate,
      suffixIcon: Icons.calendar_today,
      inputFormatter: DateInputFormatter(),
      helperText: '支援手動輸入或點擊日曆選擇',
    );
  }

  /// 構建時間輸入
  Widget _buildTimeInput() {
    return SmartDateTimeInput(
      label: '出生時間',
      hintText: 'HH:MM（例：14:30）',
      controller: _timeController,
      validator: _validateTime,
      onChanged: _updateTimeFromText,
      onTap: _selectTime,
      suffixIcon: Icons.access_time,
      inputFormatter: TimeInputFormatter(),
      helperText: '24小時制，支援手動輸入',
    );
  }

  /// 構建日期時間提示
  Widget _buildDateTimeHint() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.solarAmber.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.solarAmber.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.lightbulb_outline,
            color: AppColors.solarAmber.withValues(alpha: 0.8),
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '提示：輸入時會自動格式化，也可以點擊右側圖標使用選擇器',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建出生地輸入
  Widget _buildLocationInput() {
    return _buildInputSection(
      title: '出生地',
      icon: Icons.location_on_outlined,
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.royalIndigo.withValues(alpha: 0.3),
              ),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: AppColors.royalIndigo.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextFormField(
              controller: _placeController,
              decoration: InputDecoration(
                hintText: '請輸入出生地（例：台北市中正區）',
                hintStyle: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                prefixIcon: Icon(
                  Icons.location_on_outlined,
                  color: AppColors.royalIndigo.withValues(alpha: 0.7),
                  size: 20,
                ),
                suffixIcon: Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: IconButton(
                    icon: Icon(
                      Icons.my_location,
                      color: AppColors.solarAmber.withValues(alpha: 0.8),
                      size: 20,
                    ),
                    onPressed: _getCurrentLocation,
                    tooltip: '取得當前位置',
                  ),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '請輸入出生地';
                }
                if (value.length < 2) {
                  return '出生地至少需要2個字符';
                }
                return null;
              },
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.indigoLight.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.indigoLight.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.indigoLight.withValues(alpha: 0.8),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '請輸入詳細地址以獲得準確的經緯度，或點擊右側圖標取得當前位置',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建備註輸入
  Widget _buildNotesInput() {
    return _buildInputSection(
      title: '備註（選填）',
      icon: Icons.note_outlined,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.royalIndigo.withValues(alpha: 0.3),
          ),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: AppColors.royalIndigo.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TextFormField(
          controller: _notesController,
          decoration: InputDecoration(
            hintText: '請輸入備註（例：雙胞胎、剖腹產時間等）',
            hintStyle: TextStyle(
              color: Colors.grey[400],
              fontSize: 14,
            ),
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            prefixIcon: Icon(
              Icons.note_outlined,
              color: AppColors.royalIndigo.withValues(alpha: 0.7),
              size: 20,
            ),
          ),
          maxLines: 3,
          minLines: 3,
        ),
      ),
    );
  }

  /// 構建保存按鈕
  Widget _buildSaveButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.royalIndigo,
            AppColors.royalIndigo.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.royalIndigo.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _saveBirthData,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.save,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              widget.initialData == null ? '新增出生資料' : '保存修改',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建輸入區塊
  Widget _buildInputSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.1),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.royalIndigo.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: AppColors.royalIndigo,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  /// 構建Loading組件
  Widget _buildLoadingWidget() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.scaffoldBackground,
            AppColors.scaffoldBackground.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  width: 80,
                  height: 80,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppColors.royalIndigo.withValues(alpha: 0.3),
                    ),
                  ),
                ),
                const SizedBox(
                  width: 50,
                  height: 50,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppColors.royalIndigo,
                    ),
                  ),
                ),
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: AppColors.solarAmber,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.solarAmber.withValues(alpha: 0.3),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.person_add,
                    color: Colors.white,
                    size: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            const Text(
              '正在保存出生資料...',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColors.royalIndigo,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '正在驗證地點資訊並建立檔案',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 日期輸入格式化器
class DateInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    // 只允許數字和連字符
    final filteredText = text.replaceAll(RegExp(r'[^0-9-]'), '');

    // 自動添加連字符
    String formattedText = '';
    int digitCount = 0;

    for (int i = 0; i < filteredText.length; i++) {
      final char = filteredText[i];

      if (char == '-') {
        if (formattedText.isNotEmpty && !formattedText.endsWith('-')) {
          formattedText += char;
        }
      } else {
        // 數字
        if (digitCount == 4 && !formattedText.contains('-')) {
          formattedText += '-';
        } else if (digitCount == 6 && formattedText.split('-').length == 2) {
          formattedText += '-';
        }

        if (digitCount < 8) { // 限制最多8位數字 (YYYY-MM-DD)
          formattedText += char;
          digitCount++;
        }
      }
    }

    // 限制總長度為10 (YYYY-MM-DD)
    if (formattedText.length > 10) {
      formattedText = formattedText.substring(0, 10);
    }

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: formattedText.length),
    );
  }
}

/// 時間輸入格式化器
class TimeInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    // 只允許數字和冒號
    final filteredText = text.replaceAll(RegExp(r'[^0-9:]'), '');

    // 自動添加冒號
    String formattedText = '';
    int digitCount = 0;

    for (int i = 0; i < filteredText.length; i++) {
      final char = filteredText[i];

      if (char == ':') {
        if (formattedText.isNotEmpty && !formattedText.endsWith(':') && digitCount >= 2) {
          formattedText += char;
        }
      } else {
        // 數字
        if (digitCount == 2 && !formattedText.contains(':')) {
          formattedText += ':';
        }

        if (digitCount < 4) { // 限制最多4位數字 (HH:MM)
          formattedText += char;
          digitCount++;
        }
      }
    }

    // 限制總長度為5 (HH:MM)
    if (formattedText.length > 5) {
      formattedText = formattedText.substring(0, 5);
    }

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: formattedText.length),
    );
  }
}

/// 智能日期時間輸入組件
class SmartDateTimeInput extends StatefulWidget {
  final String label;
  final String hintText;
  final TextEditingController controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final VoidCallback? onTap;
  final IconData? suffixIcon;
  final String? helperText;
  final TextInputFormatter? inputFormatter;
  final TextInputType? keyboardType;

  const SmartDateTimeInput({
    super.key,
    required this.label,
    required this.hintText,
    required this.controller,
    this.validator,
    this.onChanged,
    this.onTap,
    this.suffixIcon,
    this.helperText,
    this.inputFormatter,
    this.keyboardType,
  });

  @override
  State<SmartDateTimeInput> createState() => _SmartDateTimeInputState();
}

class _SmartDateTimeInputState extends State<SmartDateTimeInput> {
  bool _isValid = true;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.royalIndigo,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _isValid
                ? AppColors.royalIndigo.withValues(alpha: 0.3)
                : Colors.red.withValues(alpha: 0.7),
              width: 1.5,
            ),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: AppColors.royalIndigo.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextFormField(
            controller: widget.controller,
            inputFormatters: widget.inputFormatter != null
              ? [widget.inputFormatter!]
              : null,
            keyboardType: widget.keyboardType ?? TextInputType.datetime,
            decoration: InputDecoration(
              hintText: widget.hintText,
              hintStyle: TextStyle(
                color: Colors.grey[400],
                fontSize: 14,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 14,
              ),
              suffixIcon: widget.onTap != null
                ? Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: IconButton(
                      icon: Icon(
                        widget.suffixIcon ?? Icons.edit_calendar,
                        color: AppColors.royalIndigo.withValues(alpha: 0.7),
                        size: 20,
                      ),
                      onPressed: widget.onTap,
                      tooltip: '選擇${widget.label}',
                    ),
                  )
                : null,
            ),
            validator: (value) {
              final result = widget.validator?.call(value);
              setState(() {
                _isValid = result == null;
                _errorMessage = result;
              });
              return result;
            },
            onChanged: (value) {
              // 即時驗證
              final result = widget.validator?.call(value);
              setState(() {
                _isValid = result == null;
                _errorMessage = result;
              });
              widget.onChanged?.call(value);
            },
          ),
        ),
        if (widget.helperText != null || _errorMessage != null)
          Padding(
            padding: const EdgeInsets.only(top: 6, left: 4),
            child: Text(
              _errorMessage ?? widget.helperText!,
              style: TextStyle(
                fontSize: 12,
                color: _isValid
                  ? Colors.grey[600]
                  : Colors.red[700],
              ),
            ),
          ),
      ],
    );
  }
}
