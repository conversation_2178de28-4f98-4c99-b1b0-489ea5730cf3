import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../models/astro_event.dart';
import '../../models/birth_data.dart';
import '../../models/chart_data.dart';
import '../../models/chart_type.dart';
import '../../services/astro_calendar_service.dart';
import '../../services/equinox_solstice_service.dart';
import '../../ui/AppTheme.dart';
import '../../utils/geocoding_service.dart';
import '../../viewmodels/chart_viewmodel.dart';
import '../../viewmodels/files_viewmodel.dart';
import '../../viewmodels/recent_charts_viewmodel.dart';
// 暫時註釋掉，等待創建正確的路徑
// import '../main/firdaria_page_route.dart';
import 'chart_page.dart';

// 排序字段枚舉
enum SortFieldType { id, name, birthDate, birthPlace, createdAt }

// 資料需求類
class DataRequirement {
  final String label;
  final bool isCompleted;
  final String? description;

  const DataRequirement({
    required this.label,
    required this.isCompleted,
    this.description,
  });
}

class ChartSelectionPage extends StatefulWidget {
  final BirthData? primaryPerson; // 可以為 null，讓用戶在頁面中選擇
  final List<BirthData>? allPeople;
  final BirthData? secondaryPerson; // 第二個人參數
  final ChartType? initialChartType; // 初始星盤類型
  final bool isChangingChartType; // 是否為切換星盤類型模式
  final DateTime? specificDate; // 特定日期

  const ChartSelectionPage({
    super.key,
    this.primaryPerson, // 改為可選參數
    this.allPeople,
    this.secondaryPerson,
    this.initialChartType,
    this.isChangingChartType = false,
    this.specificDate,
  });

  @override
  State<ChartSelectionPage> createState() => _ChartSelectionPageState();
}

class _ChartSelectionPageState extends State<ChartSelectionPage> {
  // 選擇的星盤類型
  ChartType _selectedChartType = ChartType.natal;

  // 主要人物（可以為 null，需要用戶選擇）
  BirthData? _primaryPerson;

  // 選擇的第二個人（用於合盤）
  BirthData? _selectedSecondaryPerson;

  // 選擇的特定日期（用於推運和天象盤）
  DateTime _selectedDate = DateTime.now();

  // 選擇的地點（用於天象盤和二分二至盤）
  String _selectedLocation = '台北市';
  double _selectedLatitude = 25.0330;
  double _selectedLongitude = 121.5654;
  final TextEditingController _locationController = TextEditingController();

  // 選擇的年份（用於二分二至盤）
  int _selectedYear = DateTime.now().year;

  // 選擇的季節（用於二分二至盤）
  String _selectedSeason = '春分'; // 春分、夏至、秋分、冬至

  // 搜尋關鍵字
  String _searchQuery = '';

  // 是否正在載入位置
  bool _isLoadingLocation = false;

  // 日月蝕選擇相關
  String _selectedEclipseFilter = '全部'; // 全部、日蝕、月蝕
  List<AstroEvent> _availableEclipses = [];
  AstroEvent? _selectedEclipse;
  bool _isLoadingEclipses = false;

  // 星盤類型對應的圖標
  final Map<ChartType, IconData> _chartTypeIcons = {
    ChartType.natal: Icons.person,
    ChartType.transit: Icons.access_time,
    ChartType.synastry: Icons.people,
    ChartType.composite: Icons.merge_type,
    ChartType.davison: Icons.compare_arrows,
    ChartType.marks: Icons.connect_without_contact,
    ChartType.secondaryProgression: Icons.timeline,
    ChartType.tertiaryProgression: Icons.show_chart,
    ChartType.solarArcDirection: Icons.wb_sunny,
    ChartType.solarReturn: Icons.replay,
    ChartType.lunarReturn: Icons.nightlight_round,
    // ChartType.electional: Icons.event_available,
    ChartType.horary: Icons.help_outline,
    ChartType.event: Icons.event,
    ChartType.firdaria: Icons.hourglass_full,
    // ChartType.fixedStars: Icons.star,
    // ChartType.harmonic: Icons.waves,
    // ChartType.draconic: Icons.change_circle,
    // ChartType.localSpace: Icons.location_on,
    ChartType.mundane: Icons.public,
    ChartType.synastrySecondary: Icons.compare,
    ChartType.synastryTertiary: Icons.compare_arrows,
    ChartType.compositeSecondary: Icons.merge,
    ChartType.compositeTertiary: Icons.merge_type,
    ChartType.davisonSecondary: Icons.compare,
    ChartType.davisonTertiary: Icons.compare_arrows,
    ChartType.marksSecondary: Icons.connect_without_contact,
    ChartType.marksTertiary: Icons.connecting_airports,
    ChartType.eclipse: Icons.brightness_2,
  };

  @override
  void initState() {
    super.initState();

    // 初始化主要人物（可能為 null）
    _primaryPerson = widget.primaryPerson;

    // 初始化地點控制器
    _locationController.text = _selectedLocation;

    // 如果有初始星盤類型，則設置為預設選擇
    if (widget.initialChartType != null) {
      _selectedChartType = widget.initialChartType!;
    }

    // 如果有第二個人參數，自動設置
    if (widget.secondaryPerson != null) {
      // 設置第二個人
      _selectedSecondaryPerson = widget.secondaryPerson;

      // 如果沒有初始星盤類型，設置預設合盤類型
      if (widget.initialChartType == null) {
        // 預設選擇合盤類型
        _selectedChartType = ChartType.synastry;
      }
    }

    // 如果有特定日期參數，設置選擇的日期
    if (widget.specificDate != null) {
      _selectedDate = widget.specificDate!;
    }

    // 如果初始星盤類型是日月蝕盤，載入事件
    if (_selectedChartType == ChartType.eclipse) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadEclipseEvents();
      });
    }
  }

  @override
  void dispose() {
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('選擇星盤類型'),
        actions: [
          // 搜尋按鈕
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // 顯示搜尋對話框
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('搜尋星盤類型'),
                  content: TextField(
                    autofocus: true,
                    decoration: const InputDecoration(
                      hintText: '輸入星盤名稱關鍵字',
                      prefixIcon: Icon(Icons.search),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                      Navigator.pop(context);
                    },
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('取消'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 人物信息卡片區域 - 使用可滾動的容器
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.25, // 限制最大高度為螢幕高度的25%
              ),
              child: SingleChildScrollView(
                child: _buildPersonsSection(),
              ),
            ),

            // 星盤類型選擇區域
            Expanded(
              child: _searchQuery.isNotEmpty
                  ? _buildSearchResultsView()
                  : _buildCompactChartsView(),
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  // 構建人物信息卡片區域
  Widget _buildPersonsSection() {
    return Column(
      mainAxisSize: MainAxisSize.min, // 確保列只佔用必要的空間
      children: [
        // 主要人物卡片或選擇按鈕
        _primaryPerson != null
            ? _buildCompactPersonCard(
                person: _primaryPerson!,
                isPrimary: true,
                onChangePerson: _changePrimaryPerson,
              )
            : _buildCompactSelectPrimaryPersonCard(),

        // 如果需要第二個人或已經選擇了第二個人，顯示第二個人卡片
        if (_selectedChartType.requiresTwoPersons || _selectedSecondaryPerson != null) ...[
          const SizedBox(height: 8),

          // 如果已經選擇了第二個人，顯示其卡片，否則顯示選擇按鈕
          _selectedSecondaryPerson != null
              ? _buildCompactPersonCard(
                  person: _selectedSecondaryPerson!,
                  isPrimary: false,
                  onChangePerson: _changeSecondaryPerson,
                )
              : _buildCompactSelectSecondaryPersonCard(),

          // 如果已經選擇了兩個人，顯示交換按鈕
          if (_selectedSecondaryPerson != null && _primaryPerson != null) ...[
            const SizedBox(height: 8),
            _buildCompactSwapPersonsButton(),
          ],
        ],
        // 添加底部空間
        const SizedBox(height: 8),
      ],
    );
  }

  // 構建線湊的人物卡片
  Widget _buildCompactPersonCard({
    required BirthData person,
    required bool isPrimary,
    required VoidCallback onChangePerson,
  }) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onChangePerson,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            children: [
              // 人物標籤
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: isPrimary ? AppColors.royalIndigo.withOpacity(0.1) : Colors.pinkAccent.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  isPrimary ? '主要' : '次要',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: isPrimary ? AppColors.royalIndigo : Colors.pinkAccent,
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // 人物信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 人物名稱
                    Text(
                      person.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    // 出生日期和地點
                    Text(
                      '${_formatDateTime(person.birthDate)} | ${person.birthPlace}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.textMedium,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // 更改按鈕
              IconButton(
                icon: const Icon(Icons.edit, size: 16),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: onChangePerson,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 構建線湊的選擇主要人物卡片
  Widget _buildCompactSelectPrimaryPersonCard() {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: _changePrimaryPerson,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            children: [
              // 人物標籤
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '主要',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // 選擇按鈕文字
              const Expanded(
                child: Text(
                  '選擇主要人物',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textMedium,
                  ),
                ),
              ),

              // 選擇按鈕
              const Icon(Icons.person_add, size: 16, color: AppColors.royalIndigo),
            ],
          ),
        ),
      ),
    );
  }

  // 構建線湊的選擇第二個人卡片
  Widget _buildCompactSelectSecondaryPersonCard() {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: _selectSecondaryPerson,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            children: [
              // 人物標籤
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.pinkAccent.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '次要',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.pinkAccent,
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // 選擇按鈕文字
              const Expanded(
                child: Text(
                  '選擇第二個人',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textMedium,
                  ),
                ),
              ),

              // 選擇按鈕
              const Icon(Icons.person_add, size: 16, color: Colors.pinkAccent),
            ],
          ),
        ),
      ),
    );
  }

  // 構建線湊的交換人物按鈕
  Widget _buildCompactSwapPersonsButton() {
    return Card(
      elevation: 1,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: _swapPersons,
        borderRadius: BorderRadius.circular(12),
        child: const Padding(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.swap_horiz, color: AppColors.royalIndigo, size: 16),
              SizedBox(width: 8),
              Text(
                '交換主次要人物',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 構建搜尋結果視圖
  Widget _buildSearchResultsView() {
    // 過濾符合搜尋關鍵字的星盤類型
    final List<ChartType> filteredChartTypes = ChartType.values
        .where((type) => type.name.toLowerCase().contains(_searchQuery.toLowerCase()))
        .toList();

    if (filteredChartTypes.isEmpty) {
      return const Center(
        child: Text('沒有符合的搜尋結果'),
      );
    }

    // 取得收藏的星盤類型
    final recentChartsViewModel = Provider.of<RecentChartsViewModel>(context);
    final favoriteChartTypes = recentChartsViewModel.favoriteCharts
        .map((record) => record.chartType)
        .toList();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜尋結果標題
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
            child: Row(
              children: [
                const Icon(Icons.search, size: 18),
                const SizedBox(width: 8),
                Text(
                  '搜尋: "$_searchQuery"',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                // 清除搜尋按鈕
                IconButton(
                  icon: const Icon(Icons.clear, size: 18),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  onPressed: () {
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                ),
              ],
            ),
          ),

          // 搜尋結果網格
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3, // 每行顯示3個卡片
                childAspectRatio: 1.2, // 稍微橫向的矩形卡片，減少高度
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: filteredChartTypes.length,
              itemBuilder: (context, index) {
                final chartType = filteredChartTypes[index];
                final isSelected = _selectedChartType == chartType;
                final isFavorite = favoriteChartTypes.contains(chartType);

                // 根據分類確定顏色
                Color categoryColor;
                if (chartType == ChartType.natal) {
                  categoryColor = Colors.blue;
                } else if (chartType.isRelationshipChart) {
                  categoryColor = Colors.pink;
                } else if (chartType.isPredictiveChart) {
                  categoryColor = Colors.purple;
                } else if (chartType.isReturnChart) {
                  categoryColor = Colors.orange;
                } else if (chartType.isEventChart) {
                  categoryColor = Colors.green;
                } else if (chartType.isSpecialChart) {
                  categoryColor = Colors.teal;
                } else {
                  categoryColor = AppColors.royalIndigo;
                }

                return _buildCompactChartTypeCard(
                  chartType,
                  isSelected,
                  categoryColor,
                  isFavorite,
                  recentChartsViewModel,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // 構建底部欄
  Widget _buildBottomBar() {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.6, // 限制最大高度為螢幕高度的60%
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 資料需求摘要卡片
            _buildDataRequirementsSummary(),
            const SizedBox(height: 12),

            // 日期選擇器
            if (_selectedChartType.requiresSpecificDate) ...[
              _buildDateSelector(),
              const SizedBox(height: 12),
            ],

            // 年份選擇器（二分二至盤專用）
            if (_selectedChartType.requiresYearSelection) ...[
              _buildYearSelector(),
              const SizedBox(height: 12),
            ],

            // 季節選擇器（二分二至盤專用）
            if (_selectedChartType == ChartType.equinoxSolstice) ...[
              _buildSeasonSelector(),
              const SizedBox(height: 12),
            ],

            // 日月蝕篩選器（日月蝕盤專用）
            if (_selectedChartType == ChartType.eclipse) ...[
              _buildEclipseFilterSelector(),
              const SizedBox(height: 12),
            ],

            // 日月蝕事件選擇器（日月蝕盤專用）
            if (_selectedChartType == ChartType.eclipse) ...[
              _buildEclipseEventSelector(),
              const SizedBox(height: 12),
            ],

            // 地點選擇器（天象盤和二分二至盤專用）
            if (_selectedChartType.requiresLocationSelection) ...[
              _buildLocationSelector(),
              const SizedBox(height: 12),
            ],

            const SizedBox(height: 4),

            // 查看星盤按鈕
            _buildViewChartButton(),
          ],
        ),
      ),
    );
  }

  // 構建資料需求摘要卡片
  Widget _buildDataRequirementsSummary() {
    final requirements = _getDataRequirements();
    if (requirements.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.royalIndigo.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.royalIndigo.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              const Icon(
                Icons.info_outline,
                size: 16,
                color: AppColors.royalIndigo,
              ),
              const SizedBox(width: 6),
              Text(
                '${_selectedChartType.name} 所需資料',
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 6,
            runSpacing: 4,
            children: requirements.map((requirement) => _buildRequirementChip(requirement)).toList(),
          ),
        ],
      ),
    );
  }

  // 構建需求標籤
  Widget _buildRequirementChip(DataRequirement requirement) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: requirement.isCompleted ? Colors.green.withOpacity(0.1) : Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: requirement.isCompleted ? Colors.green.withOpacity(0.3) : Colors.orange.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            requirement.isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
            size: 12,
            color: requirement.isCompleted ? Colors.green : Colors.orange,
          ),
          const SizedBox(width: 4),
          Text(
            requirement.label,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: requirement.isCompleted ? Colors.green.shade700 : Colors.orange.shade700,
            ),
          ),
        ],
      ),
    );
  }

  // 構建查看星盤按鈕
  Widget _buildViewChartButton() {
    final requirements = _getDataRequirements();
    final allCompleted = requirements.every((req) => req.isCompleted);
    final missingCount = requirements.where((req) => !req.isCompleted).length;

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: allCompleted ? _validateAndViewChart : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: allCompleted ? AppColors.royalIndigo : Colors.grey.shade300,
          foregroundColor: allCompleted ? Colors.white : Colors.grey.shade600,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: allCompleted ? 2 : 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (!allCompleted) ...[
              const Icon(Icons.warning_amber_rounded, size: 16),
              const SizedBox(width: 6),
            ],
            Text(
              allCompleted
                ? '查看${_selectedChartType.name}'
                : '還需完成 $missingCount 項設定',
              style: TextStyle(
                fontSize: allCompleted ? 16 : 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 獲取資料需求列表
  List<DataRequirement> _getDataRequirements() {
    final List<DataRequirement> requirements = [];

    // 檢查是否需要主要人物（天象盤和二分二至盤除外）
    if (_selectedChartType != ChartType.mundane && _selectedChartType != ChartType.equinoxSolstice) {
      requirements.add(DataRequirement(
        label: '主要人物',
        isCompleted: _primaryPerson != null,
        description: '選擇要分析的主要人物',
      ));
    }

    // 檢查是否需要第二個人
    if (_selectedChartType.requiresTwoPersons) {
      requirements.add(DataRequirement(
        label: '次要人物',
        isCompleted: _selectedSecondaryPerson != null,
        description: '選擇要比較的第二個人物',
      ));
    }

    // 檢查是否需要特定日期
    if (_selectedChartType.requiresSpecificDate) {
      requirements.add(const DataRequirement(
        label: '日期時間',
        isCompleted: true, // 預設已有當前時間
        description: '設定要分析的日期和時間',
      ));
    }

    // 檢查是否需要年份選擇（二分二至盤）
    if (_selectedChartType == ChartType.equinoxSolstice) {
      requirements.add(const DataRequirement(
        label: '年份',
        isCompleted: true, // 預設已有當前年份
        description: '選擇要分析的年份',
      ));

      requirements.add(DataRequirement(
        label: '季節',
        isCompleted: _selectedSeason.isNotEmpty,
        description: '選擇春分、夏至、秋分或冬至',
      ));
    }

    // 檢查是否需要日月蝕選擇（日月蝕盤）
    if (_selectedChartType == ChartType.eclipse) {
      requirements.add(const DataRequirement(
        label: '年份',
        isCompleted: true, // 預設已有當前年份
        description: '選擇要分析的年份',
      ));

      requirements.add(DataRequirement(
        label: '日月蝕事件',
        isCompleted: _selectedEclipse != null,
        description: '選擇要分析的日蝕或月蝕事件',
      ));
    }

    // 檢查是否需要地點選擇
    if (_selectedChartType.requiresLocationSelection) {
      requirements.add(DataRequirement(
        label: '地點',
        isCompleted: _selectedLocation.isNotEmpty && _selectedLatitude != 0.0 && _selectedLongitude != 0.0,
        description: '設定要分析的地理位置',
      ));
    }

    return requirements;
  }

  // 構建選擇器標題
  Widget _buildSelectorTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppColors.royalIndigo,
        ),
        const SizedBox(width: 6),
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
      ],
    );
  }

  // 移除了底部的第二個人選擇器，只保留人物卡片區域中的選擇功能

  // 構建日期選擇器
  Widget _buildDateSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildSelectorTitle('選擇日期', Icons.calendar_today),
        const SizedBox(height: 6),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.textLight),
          ),
          child: ListTile(
            dense: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            title: Text(
              _formatDateTime(_selectedDate),
              style: const TextStyle(fontSize: 13),
            ),
            trailing: const Icon(Icons.calendar_today, size: 18),
            onTap: () async {
              final pickedDate = await showDatePicker(
                context: context,
                initialDate: _selectedDate,
                firstDate: DateTime(1900),
                lastDate: DateTime(2100),
              );

              if (pickedDate != null) {
                final pickedTime = await showTimePicker(
                  context: context,
                  initialTime: TimeOfDay.fromDateTime(_selectedDate),
                );

                if (pickedTime != null && context.mounted) {
                  setState(() {
                    _selectedDate = DateTime(
                      pickedDate.year,
                      pickedDate.month,
                      pickedDate.day,
                      pickedTime.hour,
                      pickedTime.minute,
                    );
                  });
                }
              }
            },
          ),
        ),
      ],
    );
  }

  // 構建年份選擇器
  Widget _buildYearSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildSelectorTitle('選擇年份', Icons.date_range),
        const SizedBox(height: 6),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.textLight),
          ),
          child: ListTile(
            dense: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            title: Text(
              '$_selectedYear年',
              style: const TextStyle(fontSize: 13),
            ),
            trailing: const Icon(Icons.date_range, size: 18),
            onTap: () => _showYearPicker(),
          ),
        ),
      ],
    );
  }

  // 構建季節選擇器
  Widget _buildSeasonSelector() {
    const seasons = [
      {'name': '春分', 'description': '春季開始，晝夜平分', 'icon': Icons.local_florist},
      {'name': '夏至', 'description': '夏季高峰，白晝最長', 'icon': Icons.wb_sunny},
      {'name': '秋分', 'description': '秋季開始，晝夜平分', 'icon': Icons.eco},
      {'name': '冬至', 'description': '冬季深度，黑夜最長', 'icon': Icons.ac_unit},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildSelectorTitle('選擇季節', Icons.wb_sunny),
        const SizedBox(height: 6),
        Container(
          constraints: const BoxConstraints(
            maxHeight: 200, // 限制季節選擇器的最大高度
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.textLight),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: seasons.map((season) {
                final isSelected = _selectedSeason == season['name'];
                return InkWell(
                  onTap: () {
                    setState(() {
                      _selectedSeason = season['name'] as String;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected ? AppColors.royalIndigo.withOpacity(0.1) : null,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          season['icon'] as IconData,
                          color: isSelected ? AppColors.royalIndigo : AppColors.textMedium,
                          size: 18,
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                season['name'] as String,
                                style: TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.bold,
                                  color: isSelected ? AppColors.royalIndigo : AppColors.textDark,
                                ),
                              ),
                              Text(
                                season['description'] as String,
                                style: TextStyle(
                                  fontSize: 11,
                                  color: isSelected ? AppColors.royalIndigo : AppColors.textMedium,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          const Icon(
                            Icons.check_circle,
                            color: AppColors.royalIndigo,
                            size: 18,
                          ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  // 構建地點選擇器
  Widget _buildLocationSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: _buildSelectorTitle('選擇地點', Icons.location_on),
            ),
            TextButton.icon(
              onPressed: _isLoadingLocation ? null : _getCurrentLocation,
              icon: _isLoadingLocation
                  ? const SizedBox(
                      width: 14,
                      height: 14,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.my_location, size: 14),
              label: Text(
                _isLoadingLocation ? '定位中...' : '當前位置',
                style: const TextStyle(fontSize: 12),
              ),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.royalIndigo,
                padding: const EdgeInsets.symmetric(horizontal: 6),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        TextFormField(
          controller: _locationController,
          style: const TextStyle(fontSize: 13),
          decoration: InputDecoration(
            hintText: '請輸入地點（如：台北市）',
            hintStyle: const TextStyle(fontSize: 13),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            prefixIcon: const Icon(Icons.location_on_outlined, size: 18),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 清除按鈕
                IconButton(
                  icon: const Icon(Icons.clear, size: 18),
                  onPressed: () {
                    setState(() {
                      _locationController.clear();
                      _selectedLocation = '';
                      _selectedLatitude = 0.0;
                      _selectedLongitude = 0.0;
                    });
                  },
                  tooltip: '清除',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
                // 搜尋按鈕
                IconButton(
                  icon: _isLoadingLocation
                      ? const SizedBox(
                          width: 18,
                          height: 18,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.search, size: 18),
                  onPressed: _isLoadingLocation ? null : _searchLocation,
                  tooltip: '搜尋地點',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ),
          onChanged: (value) {
            setState(() {
              _selectedLocation = value;
            });
          },
        ),
        if (_selectedLatitude != 0.0 && _selectedLongitude != 0.0) ...[
          const SizedBox(height: 3),
          Text(
            '經緯度: ${_selectedLatitude.toStringAsFixed(4)}, ${_selectedLongitude.toStringAsFixed(4)}',
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }

  // 根據季節和年份計算具體日期時間（使用 EquinoxSolsticeService）
  Future<DateTime> _calculateSeasonDateTime(String season, int year) async {
    final equinoxSolsticeService = EquinoxSolsticeService();

    try {
      // 獲取該年份的所有季節時間
      final seasons = await equinoxSolsticeService.calculateSeasonTimes(
        year,
        latitude: _selectedLatitude,
        longitude: _selectedLongitude,
      );

      // 根據選擇的季節找到對應的精確時間
      for (final seasonData in seasons) {
        if (seasonData.seasonType.displayName == season) {
          return seasonData.dateTime;
        }
      }

      // 如果沒有找到，使用備用方法
      return _getApproximateSeasonDateTime(season, year);
    } catch (e) {
      // 如果精確計算失敗，使用備用方法
      return _getApproximateSeasonDateTime(season, year);
    }
  }

  // 備用的近似日期計算方法（與 EquinoxSolsticeService 保持一致）
  DateTime _getApproximateSeasonDateTime(String season, int year) {
    // 使用與 EquinoxSolsticeService._getApproximateSeasonDate 相同的邏輯
    switch (season) {
      case '春分':
        return DateTime(year, 3, 20, 12, 0); // 春分約在3月20日
      case '夏至':
        return DateTime(year, 6, 21, 12, 0); // 夏至約在6月21日
      case '秋分':
        return DateTime(year, 9, 23, 12, 0); // 秋分約在9月23日
      case '冬至':
        return DateTime(year, 12, 22, 12, 0); // 冬至約在12月22日（與 EquinoxSolsticeService 一致）
      default:
        return DateTime(year, 3, 20, 12, 0); // 預設春分
    }
  }

  // 獲取季節的英文名稱
  String _getSeasonEnglishName(String season) {
    switch (season) {
      case '春分':
        return 'Spring Equinox';
      case '夏至':
        return 'Summer Solstice';
      case '秋分':
        return 'Autumn Equinox';
      case '冬至':
        return 'Winter Solstice';
      default:
        return 'Spring Equinox';
    }
  }

  // 顯示年份選擇器
  Future<void> _showYearPicker() async {
    final currentYear = DateTime.now().year;
    final selectedYear = await showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('選擇年份'),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: YearPicker(
            firstDate: DateTime(currentYear - 50),
            lastDate: DateTime(currentYear + 50),
            selectedDate: DateTime(_selectedYear),
            onChanged: (DateTime dateTime) {
              Navigator.pop(context, dateTime.year);
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
        ],
      ),
    );

    if (selectedYear != null && selectedYear != _selectedYear) {
      setState(() {
        _selectedYear = selectedYear;
      });

      // 如果當前是日月蝕盤，重新載入事件
      if (_selectedChartType == ChartType.eclipse) {
        _loadEclipseEvents();
      }
    }
  }

  // 獲取當前位置
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      // 獲取當前位置的經緯度
      final coordinates = await GeocodingService.getCurrentLocation();

      if (coordinates == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('無法獲取當前位置，請確保已開啟位置服務和權限')),
          );
        }
        setState(() {
          _isLoadingLocation = false;
        });
        return;
      }

      // 根據經緯度獲取地址
      final address = await GeocodingService.getAddressFromCoordinates(
        coordinates['latitude']!,
        coordinates['longitude']!,
      );

      if (address != null && mounted) {
        setState(() {
          _selectedLocation = address;
          _selectedLatitude = coordinates['latitude']!;
          _selectedLongitude = coordinates['longitude']!;
          _locationController.text = address;
          _isLoadingLocation = false;
        });

        // 顯示成功訊息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已獲取當前位置: $address'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // 如果無法獲取地址，但有經緯度，則直接使用經緯度字符串
        if (mounted) {
          final locationStr = '緯度: ${coordinates['latitude']!.toStringAsFixed(6)}, 經度: ${coordinates['longitude']!.toStringAsFixed(6)}';
          setState(() {
            _selectedLocation = locationStr;
            _selectedLatitude = coordinates['latitude']!;
            _selectedLongitude = coordinates['longitude']!;
            _locationController.text = locationStr;
            _isLoadingLocation = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('無法獲取地址名稱，已使用經緯度: $locationStr'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('獲取位置時出錯: $e')),
        );
      }
    }
  }

  // 搜尋地點
  Future<void> _searchLocation() async {
    if (_locationController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('請輸入地點名稱')),
      );
      return;
    }

    setState(() {
      _isLoadingLocation = true;
    });

    try {
      // 使用 GeocodingService 將地點名稱轉換為經緯度
      Map<String, double> coordinates = await GeocodingService.getCoordinatesFromAddress(
        _locationController.text.trim(),
      );

      if (coordinates['latitude'] == null || coordinates['longitude'] == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('無法找到該地點，請檢查地點名稱')),
          );
        }
        setState(() {
          _isLoadingLocation = false;
        });
        return;
      }

      setState(() {
        _selectedLocation = _locationController.text.trim();
        _selectedLatitude = coordinates['latitude']!;
        _selectedLongitude = coordinates['longitude']!;
        _isLoadingLocation = false;
      });

      // 顯示成功訊息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已找到地點: $_selectedLocation'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('搜尋地點時出錯: $e')),
        );
      }
    }
  }



  // 驗證並查看星盤
  Future<void> _validateAndViewChart() async {
    // 天象盤、二分二至盤和日月蝕盤不需要選擇人物，其他星盤需要
    if (_selectedChartType != ChartType.mundane &&
        _selectedChartType != ChartType.equinoxSolstice &&
        _selectedChartType != ChartType.eclipse &&
        _primaryPerson == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('請先選擇主要人物')),
      );
      return;
    }

    // 檢查日月蝕盤是否選擇了事件
    if (_selectedChartType == ChartType.eclipse && _selectedEclipse == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('請選擇日月蝕事件')),
      );
      return;
    }

    // 檢查是否需要第二個人但未選擇
    if (_selectedChartType.requiresTwoPersons &&
        _selectedSecondaryPerson == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('請選擇第二個人')),
      );
      return;
    }

    // 法達盤也使用 ChartPage，因為我們已經將法達盤功能整合到 ChartPage 中

    // 創建 ChartData 對象
    ChartData chartData;

    if (_selectedChartType == ChartType.mundane) {
      // 天象盤使用選擇的時間和地點創建虛擬人物
      chartData = ChartData(
        chartType: _selectedChartType,
        primaryPerson: BirthData(
          id: 'mundane_${_selectedDate.millisecondsSinceEpoch}',
          name: '天象盤',
          birthDate: _selectedDate,
          birthPlace: _selectedLocation,
          latitude: _selectedLatitude,
          longitude: _selectedLongitude,
        ),
        specificDate: _selectedDate,
      );
    } else if (_selectedChartType == ChartType.equinoxSolstice) {
      // 二分二至盤使用選擇的年份、季節和地點創建虛擬人物
      final seasonDateTime = await _calculateSeasonDateTime(_selectedSeason, _selectedYear);

      chartData = ChartData(
        chartType: _selectedChartType,
        primaryPerson: BirthData(
          id: 'equinox_solstice_${_selectedYear}_${_selectedSeason}_${_selectedLocation.hashCode}',
          name: '$_selectedYear年$_selectedSeason盤',
          birthDate: seasonDateTime,
          birthPlace: _selectedLocation,
          latitude: _selectedLatitude,
          longitude: _selectedLongitude,
        ),
        secondaryPerson: _primaryPerson, // 如果有選擇人物，用於比較
        specificDate: seasonDateTime,
      );
    } else if (_selectedChartType == ChartType.eclipse) {
      // 日月蝕盤邏輯
      chartData = ChartData(
        chartType: _selectedChartType,
        primaryPerson: BirthData(
          id: 'eclipse_${_selectedEclipse!.id}',
          name: _selectedEclipse!.title,
          birthDate: _selectedEclipse!.dateTime,
          birthPlace: '全球', // 日月蝕是全球性事件
          latitude: 0.0, // 使用地心坐標
          longitude: 0.0,
        ),
        secondaryPerson: _primaryPerson, // 如果有選擇人物，用於比較
        specificDate: _selectedEclipse!.dateTime,
      );
    } else {
      // 其他星盤類型使用正常邏輯
      chartData = ChartData(
        chartType: _selectedChartType,
        primaryPerson: _primaryPerson!, // 已經驗證不為 null
        secondaryPerson: _selectedSecondaryPerson,
        specificDate:
            _selectedChartType.requiresSpecificDate ? _selectedDate : null,
      );
    }

    // 如果是切換星盤類型模式，返回 ChartData 而不導航到新頁面
    if (widget.isChangingChartType) {
      Navigator.pop(context, chartData);
    } else {
      // 導航到星盤頁面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) => ChartViewModel.withChartData(initialChartData: chartData),
            child: ChartPage(chartData: chartData),
          ),
        ),
      );
    }
  }

  // 更改主要人物
  void _changePrimaryPerson() async {
    final filesViewModel = Provider.of<FilesViewModel>(context, listen: false);
    await filesViewModel.loadBirthData();

    if (context.mounted) {
      // 過濾掉第二個人（如果有）
      final filteredList = _selectedSecondaryPerson != null
          ? filesViewModel.birthDataList.where((data) => data.id != _selectedSecondaryPerson!.id).toList()
          : filesViewModel.birthDataList;

      final BirthData? selectedPerson = await _showPersonSelectionDialog(
        filteredList,
        title: '選擇主要人物',
        buttonColor: AppColors.royalIndigo,
      );

      if (selectedPerson != null && mounted) {
        setState(() {
          _primaryPerson = selectedPerson;
        });
      }
    }
  }

  // 更改次要人物
  void _changeSecondaryPerson() async {
    final filesViewModel = Provider.of<FilesViewModel>(context, listen: false);
    await filesViewModel.loadBirthData();

    if (context.mounted) {
      // 過濾掉主要人物（如果有）
      final filteredList = _primaryPerson != null
          ? filesViewModel.birthDataList
              .where((data) => data.id != _primaryPerson!.id)
              .toList()
          : filesViewModel.birthDataList;

      final BirthData? selectedPerson = await _showPersonSelectionDialog(
        filteredList,
        title: '選擇次要人物',
        buttonColor: Colors.pinkAccent,
      );

      if (selectedPerson != null && mounted) {
        setState(() {
          _selectedSecondaryPerson = selectedPerson;
        });
      }
    }
  }

  // 選擇第二個人
  void _selectSecondaryPerson() async {
    final filesViewModel = Provider.of<FilesViewModel>(context, listen: false);
    await filesViewModel.loadBirthData();

    if (context.mounted) {
      // 過濾掉主要人物（如果有）
      final filteredList = _primaryPerson != null
          ? filesViewModel.birthDataList
              .where((data) => data.id != _primaryPerson!.id)
              .toList()
          : filesViewModel.birthDataList;

      final BirthData? selectedPerson = await _showPersonSelectionDialog(
        filteredList,
        title: '選擇次要人物',
        buttonColor: Colors.pinkAccent,
      );

      if (selectedPerson != null && mounted) {
        setState(() {
          _selectedSecondaryPerson = selectedPerson;
        });
      }
    }
  }

  // 交換主次要人物
  void _swapPersons() {
    if (_selectedSecondaryPerson == null || _primaryPerson == null) return;

    setState(() {
      final temp = _primaryPerson;
      _primaryPerson = _selectedSecondaryPerson!;
      _selectedSecondaryPerson = temp;
    });
  }

  // 構建緊湊的星盤類型視圖
  Widget _buildCompactChartsView() {
    // 按分類組織星盤類型，但不顯示分類標題
    final Map<String, List<ChartType>> categorizedCharts = {
      '特殊星盤': [ChartType.mundane, ChartType.firdaria, ChartType.equinoxSolstice, ChartType.eclipse],
      '基本盤': [ChartType.natal, ChartType.transit],
      '合盤': [ChartType.synastry, ChartType.composite, ChartType.davison, ChartType.marks],
      '推運': [ChartType.secondaryProgression, ChartType.tertiaryProgression, ChartType.solarArcDirection],
      '返照盤': [ChartType.solarReturn, ChartType.lunarReturn],
      '合盤推運': [
        ChartType.synastrySecondary, ChartType.synastryTertiary,
        ChartType.compositeSecondary, ChartType.compositeTertiary,
        ChartType.davisonSecondary, ChartType.davisonTertiary,
        ChartType.marksSecondary, ChartType.marksTertiary,
      ],
    };

    // 將所有星盤類型展平為一個列表
    final List<ChartType> allChartTypes = [];
    categorizedCharts.forEach((category, types) {
      allChartTypes.addAll(types);
    });

    // 將收藏的星盤類型放在最前面
    final recentChartsViewModel = Provider.of<RecentChartsViewModel>(context);
    final favoriteChartTypes = recentChartsViewModel.favoriteCharts
        .map((record) => record.chartType)
        .toList();

    // 將收藏的星盤類型從列表中移除，以避免重複
    allChartTypes.removeWhere((type) => favoriteChartTypes.contains(type));

    // 將收藏的星盤類型放在最前面
    final List<ChartType> sortedChartTypes = [...favoriteChartTypes, ...allChartTypes];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4, // 每行顯示3個卡片
          childAspectRatio: 1.2, // 稍微橫向的矩形卡片，減少高度
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount: sortedChartTypes.length,
        itemBuilder: (context, index) {
          final chartType = sortedChartTypes[index];
          final isSelected = _selectedChartType == chartType;
          final isFavorite = favoriteChartTypes.contains(chartType);

          // 根據分類確定顏色
          Color categoryColor;
          if (chartType == ChartType.natal) {
            categoryColor = Colors.blue;
          } else if (chartType.isRelationshipChart) {
            categoryColor = Colors.pink;
          } else if (chartType.isPredictiveChart) {
            categoryColor = Colors.purple;
          } else if (chartType.isReturnChart) {
            categoryColor = Colors.orange;
          } else if (chartType.isEventChart) {
            categoryColor = Colors.green;
          } else if (chartType.isSpecialChart) {
            categoryColor = Colors.teal;
          } else {
            categoryColor = AppColors.royalIndigo;
          }

          return _buildCompactChartTypeCard(
            chartType,
            isSelected,
            categoryColor,
            isFavorite,
            recentChartsViewModel,
          );
        },
      ),
    );
  }

  // 構建線湊的星盤類型卡片
  Widget _buildCompactChartTypeCard(
    ChartType chartType,
    bool isSelected,
    Color categoryColor,
    bool isFavorite,
    RecentChartsViewModel viewModel,
  ) {
    return Card(
      elevation: isSelected ? 4 : 1,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isSelected ? categoryColor : Colors.transparent,
          width: isSelected ? 2.0 : 0.0,
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedChartType = chartType;

            // 如果切換到不需要第二個人的星盤類型，清除選擇的第二個人
            if (!chartType.requiresTwoPersons) {
              _selectedSecondaryPerson = null;
            }

            // 如果選擇日月蝕盤，重置相關狀態並載入事件
            if (chartType == ChartType.eclipse) {
              _selectedEclipse = null;
              _availableEclipses.clear();
              _selectedEclipseFilter = '全部';
            }
          });

          // 如果選擇日月蝕盤，載入事件
          if (chartType == ChartType.eclipse) {
            _loadEclipseEvents();
          }
        },
        onLongPress: () async {
          // 長按切換收藏狀態
          // 檢查是否選擇了主要人物（天象盤、二分二至盤和日月蝕盤除外）
          if (chartType != ChartType.mundane &&
              chartType != ChartType.equinoxSolstice &&
              chartType != ChartType.eclipse &&
              _primaryPerson == null) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('請先選擇主要人物')),
            );
            return;
          }

          // 檢查日月蝕盤是否選擇了事件
          if (chartType == ChartType.eclipse && _selectedEclipse == null) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('請選擇日月蝕事件')),
            );
            return;
          }

          // 先創建 ChartData 對象
          ChartData chartData;

          if (chartType == ChartType.mundane) {
            // 天象盤邏輯
            chartData = ChartData(
              chartType: chartType,
              primaryPerson: BirthData(
                id: 'mundane_${_selectedDate.millisecondsSinceEpoch}',
                name: '天象盤',
                birthDate: _selectedDate,
                birthPlace: _selectedLocation,
                latitude: _selectedLatitude,
                longitude: _selectedLongitude,
              ),
              specificDate: _selectedDate,
            );
          } else if (chartType == ChartType.equinoxSolstice) {
            // 二分二至盤邏輯
            final seasonDateTime = await _calculateSeasonDateTime(_selectedSeason, _selectedYear);
            chartData = ChartData(
              chartType: chartType,
              primaryPerson: BirthData(
                id: 'equinox_solstice_${_selectedYear}_${_selectedSeason}_${_selectedLocation.hashCode}',
                name: '$_selectedYear年$_selectedSeason盤',
                birthDate: seasonDateTime,
                birthPlace: _selectedLocation,
                latitude: _selectedLatitude,
                longitude: _selectedLongitude,
              ),
              secondaryPerson: _primaryPerson,
              specificDate: seasonDateTime,
            );
          } else if (chartType == ChartType.eclipse) {
            // 日月蝕盤邏輯
            chartData = ChartData(
              chartType: chartType,
              primaryPerson: BirthData(
                id: 'eclipse_${_selectedEclipse!.id}',
                name: _selectedEclipse!.title,
                birthDate: _selectedEclipse!.dateTime,
                birthPlace: '全球',
                latitude: 0.0,
                longitude: 0.0,
              ),
              secondaryPerson: _primaryPerson,
              specificDate: _selectedEclipse!.dateTime,
            );
          } else {
            // 其他星盤類型
            chartData = ChartData(
              chartType: chartType,
              primaryPerson: _primaryPerson!,
              secondaryPerson: _selectedSecondaryPerson,
              specificDate: _selectedChartType.requiresSpecificDate ? _selectedDate : null,
            );
          }

          // 將星盤添加到最近使用的記錄中
          viewModel.addOrUpdateRecentChart(chartData);

          // 顯示提示
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('已添加${chartType.name}到最近使用的記錄中')),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // 星盤卡片內容
            Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 4.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min, // 確保列使用最小高度
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 圖標
                    Icon(
                      _chartTypeIcons[chartType] ?? Icons.star,
                      color: categoryColor,
                      size: 20, // 縮小圖標
                    ),
                    const SizedBox(height: 2), // 減少間距
                    // 星盤名稱
                    Text(
                      chartType.name,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 12, // 縮小字體
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        color: isSelected ? categoryColor : AppColors.textDark,
                      ),
                      maxLines: 1, // 限制為一行
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),

            // 收藏圖標
            if (isFavorite)
              const Positioned(
                top: 1,
                right: 1,
                child: Icon(
                  Icons.star,
                  color: Colors.amber,
                  size: 12, // 縮小圖標
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 顯示人物選擇對話框
  Future<BirthData?> _showPersonSelectionDialog(
    List<BirthData> birthDataList, {
    required String title,
    required Color buttonColor,
  }) async {
    // 從 SharedPreferences 加載排序設置
    final prefs = await SharedPreferences.getInstance();
    final sortFieldIndex = prefs.getInt('personSelectionSortField') ?? SortFieldType.name.index;
    final isAscendingPref = prefs.getBool('personSelectionIsAscending') ?? true;

    // 排序方式
    SortFieldType currentSortField = SortFieldType.values[sortFieldIndex]; // 使用儲存的排序方式
    bool isAscending = isAscendingPref; // 使用儲存的排序方向

    // 搜尋關鍵字
    String searchQuery = '';

    // 排序函數
    void sortList(List<BirthData> list, SortFieldType field, bool ascending) {
      switch (field) {
        case SortFieldType.id:
          list.sort((a, b) => ascending ? a.id.compareTo(b.id) : b.id.compareTo(a.id));
          break;
        case SortFieldType.name:
          list.sort((a, b) => ascending ? a.name.compareTo(b.name) : b.name.compareTo(a.name));
          break;
        case SortFieldType.birthDate:
          list.sort((a, b) => ascending ? a.birthDate.compareTo(b.birthDate) : b.birthDate.compareTo(a.birthDate));
          break;
        case SortFieldType.birthPlace:
          list.sort((a, b) => ascending ? a.birthPlace.compareTo(b.birthPlace) : b.birthPlace.compareTo(a.birthPlace));
          break;
        case SortFieldType.createdAt:
          list.sort((a, b) => ascending ? a.createdAt.compareTo(b.createdAt) : b.createdAt.compareTo(a.createdAt));
          break;
      }
    }

    // 初始排序
    sortList(birthDataList, currentSortField, isAscending);

    return showDialog<BirthData>(
      context: context,
      builder: (context) {
        // 使用 StatefulBuilder 以便在對話框內更新狀態
        return StatefulBuilder(
          builder: (context, setState) {
            // 過濾列表
            final filteredList = searchQuery.isEmpty
                ? birthDataList
                : birthDataList.where((person) {
                    return person.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
                        person.birthPlace.toLowerCase().contains(searchQuery.toLowerCase()) ||
                        _formatDateTime(person.birthDate).contains(searchQuery);
                  }).toList();

            return AlertDialog(
              title: Text(title),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              content: SizedBox(
                width: double.maxFinite,
                height: MediaQuery.of(context).size.height * 0.6, // 增加高度以容納排序選項
                child: Column(
                  children: [
                    // 搜索欄
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: TextField(
                        decoration: InputDecoration(
                          hintText: '搜索人物',
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          contentPadding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        onChanged: (value) {
                          setState(() {
                            searchQuery = value;
                          });
                        },
                      ),
                    ),

                    // 排序選項
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          const Text('排序：', style: TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(width: 8),
                          // 排序下拉選單
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: DropdownButton<SortFieldType>(
                                isExpanded: true,
                                underline: const SizedBox(),
                                value: currentSortField,
                                items: const [
                                  DropdownMenuItem(value: SortFieldType.id, child: Text('ID')),
                                  DropdownMenuItem(value: SortFieldType.name, child: Text('姓名')),
                                  DropdownMenuItem(value: SortFieldType.birthDate, child: Text('出生日期')),
                                  DropdownMenuItem(value: SortFieldType.birthPlace, child: Text('出生地點')),
                                  DropdownMenuItem(value: SortFieldType.createdAt, child: Text('建立時間')),
                                ],
                                onChanged: (value) async {
                                  if (value != null) {
                                    // 儲存排序設置
                                    final prefs = await SharedPreferences.getInstance();
                                    await prefs.setInt('personSelectionSortField', value.index);

                                    setState(() {
                                      currentSortField = value;
                                      sortList(birthDataList, currentSortField, isAscending);
                                    });
                                  }
                                },
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          // 升/降序按鈕
                          IconButton(
                            icon: Icon(isAscending ? Icons.arrow_upward : Icons.arrow_downward),
                            onPressed: () async {
                              // 儲存排序方向
                              final prefs = await SharedPreferences.getInstance();
                              await prefs.setBool('personSelectionIsAscending', !isAscending);

                              setState(() {
                                isAscending = !isAscending;
                                sortList(birthDataList, currentSortField, isAscending);
                              });
                            },
                            tooltip: isAscending ? '升序排列' : '降序排列',
                          ),
                        ],
                      ),
                    ),

                    // 人物列表
                    Expanded(
                      child: filteredList.isEmpty
                          ? const Center(child: Text('沒有符合條件的人物'))
                          : ListView.builder(
                              shrinkWrap: true,
                              itemCount: filteredList.length,
                              itemBuilder: (context, index) {
                                final person = filteredList[index];
                                return Card(
                                  margin: const EdgeInsets.only(bottom: 8),
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                  child: ListTile(
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                    leading: CircleAvatar(
                                      backgroundColor: buttonColor,
                                      child: Text(
                                        person.name.isNotEmpty ? person.name.substring(0, 1) : '?',
                                        style: const TextStyle(color: Colors.white),
                                      ),
                                    ),
                                    title: Text(
                                      person.name,
                                      style: const TextStyle(fontWeight: FontWeight.bold),
                                    ),
                                    subtitle: Text('${_formatDateTime(person.birthDate)} | ${person.birthPlace}'),
                                    onTap: () {
                                      Navigator.pop(context, person);
                                    },
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  style: TextButton.styleFrom(foregroundColor: AppColors.textMedium),
                  child: const Text('取消'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // 構建日月蝕篩選器
  Widget _buildEclipseFilterSelector() {
    const filters = ['全部', '日蝕', '月蝕'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildSelectorTitle('篩選類型', Icons.filter_list),
        const SizedBox(height: 6),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.textLight),
          ),
          child: Row(
            children: filters.map((filter) {
              final isSelected = _selectedEclipseFilter == filter;
              return Expanded(
                child: InkWell(
                  onTap: () {
                    setState(() {
                      _selectedEclipseFilter = filter;
                      _selectedEclipse = null; // 重置選擇的日月蝕事件
                    });
                    _loadEclipseEvents(); // 重新載入事件
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected ? AppColors.royalIndigo.withOpacity(0.1) : null,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      filter,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                        color: isSelected ? AppColors.royalIndigo : AppColors.textMedium,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  // 構建日月蝕事件選擇器
  Widget _buildEclipseEventSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildSelectorTitle('選擇日月蝕事件', Icons.brightness_2),
            if (_isLoadingEclipses)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
        const SizedBox(height: 6),
        Container(
          constraints: const BoxConstraints(
            maxHeight: 200, // 限制事件選擇器的最大高度
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.textLight),
          ),
          child: _availableEclipses.isEmpty
              ? Container(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    _isLoadingEclipses ? '載入中...' : '該年份沒有${_selectedEclipseFilter == '全部' ? '日月蝕' : _selectedEclipseFilter}事件',
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 13,
                      color: AppColors.textMedium,
                    ),
                  ),
                )
              : SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: _availableEclipses.map((eclipse) {
                      final isSelected = _selectedEclipse?.id == eclipse.id;
                      return InkWell(
                        onTap: () {
                          setState(() {
                            _selectedEclipse = eclipse;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          decoration: BoxDecoration(
                            color: isSelected ? AppColors.royalIndigo.withOpacity(0.1) : null,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                eclipse.type == AstroEventType.eclipse ? Icons.brightness_2 : Icons.brightness_2,
                                color: isSelected ? AppColors.royalIndigo : AppColors.textMedium,
                                size: 18,
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      eclipse.title,
                                      style: TextStyle(
                                        fontSize: 13,
                                        fontWeight: FontWeight.bold,
                                        color: isSelected ? AppColors.royalIndigo : AppColors.textDark,
                                      ),
                                    ),
                                    Text(
                                      _formatDateTime(eclipse.dateTime),
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: isSelected ? AppColors.royalIndigo : AppColors.textMedium,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (isSelected)
                                const Icon(
                                  Icons.check_circle,
                                  color: AppColors.royalIndigo,
                                  size: 18,
                                ),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
        ),
      ],
    );
  }

  // 載入日月蝕事件
  Future<void> _loadEclipseEvents() async {
    setState(() {
      _isLoadingEclipses = true;
      _availableEclipses.clear();
    });

    try {
      final astroCalendarService = AstroCalendarService();
      final startDate = DateTime(_selectedYear, 1, 1);
      final endDate = DateTime(_selectedYear, 12, 31);

      // 獲取該年份的所有天象事件
      final events = <AstroEvent>[];

      // 逐月獲取事件以避免性能問題
      for (int month = 1; month <= 12; month++) {
        final monthlyEvents = await astroCalendarService.getMonthlyEvents(
          _selectedYear,
          month,
          latitude: 0.0, // 使用地心坐標
          longitude: 0.0,
        );
        events.addAll(monthlyEvents);
      }

      // 篩選日月蝕事件
      final eclipseEvents = events.where((event) {
        if (event.type != AstroEventType.eclipse) return false;

        // 根據篩選器進一步篩選
        if (_selectedEclipseFilter == '全部') {
          return true;
        } else if (_selectedEclipseFilter == '日蝕') {
          // 檢查是否為日蝕（通過標題或其他屬性判斷）
          return event.title.contains('日') && !event.title.contains('月');
        } else if (_selectedEclipseFilter == '月蝕') {
          // 檢查是否為月蝕
          return event.title.contains('月') && !event.title.contains('日');
        }
        return false;
      }).toList();

      // 按日期排序
      eclipseEvents.sort((a, b) => a.dateTime.compareTo(b.dateTime));

      setState(() {
        _availableEclipses = eclipseEvents;
        _isLoadingEclipses = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingEclipses = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('載入日月蝕事件時出錯: $e')),
        );
      }
    }
  }
}
