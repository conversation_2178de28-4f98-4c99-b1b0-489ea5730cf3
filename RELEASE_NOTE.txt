Release Note:
feat: 改善星盤選擇頁面，新增資料需求摘要與提示

此提交包含以下變更：

*   **新增：** `DataRequirement` 類別，用於表示星盤所需的資料，包含標籤、完成狀態和描述。
*   **新增：** `_buildDataRequirementsSummary()` 方法，用於構建資料需求摘要卡片，顯示星盤所需的資料及其完成狀態。
*   **新增：** `_buildRequirementChip()` 方法，用於構建單個資料需求標籤，顯示需求名稱和完成狀態。
*   **新增：** `_getDataRequirements()` 方法，用於獲取特定星盤類型所需的資料列表。
*   **修改：** `_buildBottomBar()` 方法，將資料需求摘要卡片加入底部欄，並限制底部欄的最大高度。
*   **修改：** `_buildViewChartButton()` 方法，根據資料需求完成狀態啟用或停用「查看星盤」按鈕，並顯示相關提示。
*   **修改：** 調整各選擇器元件的樣式，使其更緊湊並提升易用性。
*   **新增：** `_buildSelectorTitle()` 方法，用於構建選擇器標題。

這些變更旨在改善星盤選擇頁面的使用者體驗，讓使用者更清楚了解星盤所需的資料，並引導使用者完成必要的設定。
