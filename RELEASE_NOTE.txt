Release Note:
feat: 新增主題式星盤資訊複製功能

這個 commit 增加了以下功能：

*   **新增主題複製對話框 (theme_copy_dialog.dart):**  提供使用者選擇不同的占星主題 (例如：感情關係、職涯方向、自我探索等)，並顯示各主題的簡介、分析重點，以及複製按鈕。
*   **擴充 ChartViewModel (chart_viewmodel.dart):**
    *   新增 `copyThemeChartInfo` 方法，用於根據使用者選擇的主題，生成包含主題描述、分析要點、基本星盤資料，以及主題特定分析的文字，並複製到剪貼簿。
    *   新增 `generateThemeChartInfoText` 方法，用於生成主題相關的星盤資訊文本。
    *   新增 `_generateThemeSpecificAnalysis` 方法，根據主題鍵值，調用不同的分析方法。
    *   新增 `_generateRelationshipAnalysis`、`_generateCareerAnalysis`、`_generateSelfExplorationAnalysis`、`_generateAnnualFortuneAnalysis`、`_generateWealthAnalysis` 等方法，用於生成各主題的分析內容。
*   **修改 ChartPage (chart_page.dart):**
    *   在選單中新增 "主題星盤資訊" 選項，點擊後會顯示主題複製對話框。
    *   點擊 "主題星盤資訊" 後，會呼叫 `copyThemeChartInfo` 方法，複製相關資訊到剪貼簿，並顯示成功訊息。

這個 commit 讓使用者可以更方便地複製特定主題的星盤資訊，並用於占星分析。
